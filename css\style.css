/* AIvent - Modern Dark Theme Styles */

/* Global Styles */
:root {
    --primary-color: #00d4ff;
    --secondary-color: #ff6b6b;
    --dark-bg: #0a0a0a;
    --dark-card: #1a1a1a;
    --text-light: #ffffff;
    --text-muted: #b0b0b0;
    --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --gradient-secondary: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.dark-scheme {
    background: var(--dark-bg);
    color: var(--text-light);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Navigation */
.navbar {
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar-brand img {
    transition: transform 0.3s ease;
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    color: var(--text-light) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background: rgba(0, 212, 255, 0.1);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: 2rem 0;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 1s ease;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
    animation: fadeInUp 1s ease 0.2s both;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
    max-width: 500px;
    animation: fadeInUp 1s ease 0.4s both;
}

.hero-buttons {
    animation: fadeInUp 1s ease 0.6s both;
}

/* Floating Elements */
.floating-elements {
    position: relative;
    height: 400px;
}

.floating-card {
    position: absolute;
    background: var(--dark-card);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;
    transition: transform 0.3s ease;
}

.floating-card:hover {
    transform: translateY(-10px) scale(1.05);
    border-color: var(--primary-color);
}

.floating-card:nth-child(1) {
    top: 50px;
    right: 100px;
    animation-delay: 0s;
}

.floating-card:nth-child(2) {
    top: 200px;
    right: 50px;
    animation-delay: 2s;
}

.floating-card:nth-child(3) {
    top: 150px;
    right: 200px;
    animation-delay: 4s;
}

.floating-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: block;
}

.floating-card span {
    font-size: 0.9rem;
    font-weight: 600;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-down {
    color: var(--text-muted);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
    text-decoration: none;
}

.scroll-down:hover {
    color: var(--primary-color);
}

/* Sections */
section {
    padding: 5rem 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-muted);
    margin-bottom: 3rem;
}

/* About Section */
.about-content h3 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.stats-row {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.about-image video {
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Feature Cards */
.feature-card {
    background: var(--dark-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.feature-card p {
    color: var(--text-muted);
    line-height: 1.6;
}

/* Countdown Timer */
.countdown-container {
    background: var(--dark-card);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    backdrop-filter: blur(10px);
}

.countdown-timer {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.time-unit {
    background: var(--gradient-primary);
    border-radius: 15px;
    padding: 1.5rem;
    min-width: 100px;
    color: white;
}

.time-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1;
}

.time-label {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.8;
}

.event-info h4 {
    color: var(--text-light);
    margin-bottom: 1rem;
}

.event-info p {
    color: var(--text-muted);
    margin-bottom: 2rem;
}

/* Newsletter Section */
.newsletter-section {
    background: var(--gradient-primary);
}

.newsletter-section h3 {
    color: white;
    font-size: 2rem;
    margin-bottom: 1rem;
}

.newsletter-section p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
}

.newsletter-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 25px 0 0 25px;
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.newsletter-form .btn {
    border-radius: 0 25px 25px 0;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

/* Footer */
.footer {
    background: var(--dark-card);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.social-links a {
    color: var(--text-muted);
    font-size: 1.2rem;
    margin-left: 1rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-color);
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .countdown-timer {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .time-unit {
        min-width: 80px;
        padding: 1rem;
    }

    .time-number {
        font-size: 2rem;
    }

    .stats-row {
        flex-direction: column;
        gap: 1rem;
    }

    .floating-elements {
        display: none;
    }

    .newsletter-form .input-group {
        flex-direction: column;
    }

    .newsletter-form .form-control,
    .newsletter-form .btn {
        border-radius: 25px;
        margin-bottom: 1rem;
    }
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
    padding: 6rem 0 4rem;
}

.page-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 1.2rem;
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.breadcrumb {
    background: none;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--text-muted);
}

/* Ticket Cards */
.ticket-card {
    background: var(--dark-card);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.ticket-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.ticket-card.featured {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, var(--dark-card) 100%);
    transform: scale(1.05);
}

.ticket-card.premium {
    border-color: var(--secondary-color);
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, var(--dark-card) 100%);
}

.ticket-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.ticket-header h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.ticket-price {
    margin-bottom: 1rem;
}

.ticket-price .currency {
    font-size: 1.5rem;
    color: var(--text-muted);
    vertical-align: top;
}

.ticket-price .amount {
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-color);
}

.ticket-price .period {
    font-size: 1rem;
    color: var(--text-muted);
}

.ticket-description {
    color: var(--text-muted);
    margin-bottom: 2rem;
}

.ticket-features {
    flex-grow: 1;
    margin-bottom: 2rem;
}

.ticket-features ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.ticket-features li {
    padding: 0.5rem 0;
    color: var(--text-light);
}

.ticket-features li i {
    color: var(--primary-color);
    margin-right: 0.5rem;
    width: 16px;
}

.ticket-note {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-top: 1rem;
    margin-bottom: 0;
}

/* Info Cards */
.info-card {
    background: var(--dark-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
}

.info-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.info-icon i {
    font-size: 1.5rem;
    color: white;
}

.info-card h4 {
    color: var(--text-light);
    margin-bottom: 1rem;
}

.info-card p {
    color: var(--text-muted);
    margin-bottom: 0;
}

/* Accordion Styles */
.accordion-item {
    background: var(--dark-card);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    margin-bottom: 1rem;
}

.accordion-button {
    background: transparent;
    color: var(--text-light);
    border: none;
    padding: 1.5rem;
    font-weight: 600;
}

.accordion-button:not(.collapsed) {
    background: rgba(0, 212, 255, 0.1);
    color: var(--primary-color);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: var(--primary-color);
}

.accordion-body {
    background: var(--dark-card);
    color: var(--text-muted);
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Additional Button Styles */
.btn-gradient-secondary {
    background: var(--gradient-secondary);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-gradient-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
    color: white;
}
