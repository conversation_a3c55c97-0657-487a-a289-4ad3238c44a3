/* AIvent Color Scheme - Futuristic Blue Theme */
:root {
  --main-color: #00d4ff;
  --secondary-color: #0a0a0a;
  --accent-color: #ff6b6b;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #1a1a1a;
  --muted-color: #b0b0b0;
}

/* Theme-specific overrides */
.bg-primary {
  background: linear-gradient(135deg, var(--main-color) 0%, #0099cc 100%) !important;
}

.text-primary {
  color: var(--main-color) !important;
}

.border-primary {
  border-color: var(--main-color) !important;
}

/* Button variants */
.btn-primary {
  background: linear-gradient(135deg, var(--main-color) 0%, #0099cc 100%);
  border-color: var(--main-color);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0099cc 0%, var(--main-color) 100%);
  border-color: #0099cc;
}

/* Custom gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--main-color) 0%, #0099cc 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--accent-color) 0%, #ee5a24 100%);
}
