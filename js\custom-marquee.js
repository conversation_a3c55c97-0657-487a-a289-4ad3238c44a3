// AIvent Custom Marquee and Additional Effects
document.addEventListener('DOMContentLoaded', function() {

    // Create a dynamic tech marquee for the hero section
    function createTechMarquee() {
        const techTerms = [
            'Artificial Intelligence', 'Machine Learning', 'Deep Learning', 'Neural Networks',
            'Computer Vision', 'Natural Language Processing', 'Robotics', 'Data Science',
            'Blockchain', 'IoT', 'Quantum Computing', 'Edge Computing', 'Cloud AI',
            'Reinforcement Learning', 'Generative AI', 'Computer Graphics'
        ];

        // Create marquee container
        const marqueeContainer = document.createElement('div');
        marqueeContainer.className = 'tech-marquee';
        marqueeContainer.style.cssText = `
            position: absolute;
            top: 20%;
            left: 0;
            width: 100%;
            overflow: hidden;
            opacity: 0.1;
            z-index: 1;
            pointer-events: none;
        `;

        // Create marquee content
        const marqueeContent = document.createElement('div');
        marqueeContent.style.cssText = `
            display: flex;
            animation: marqueeScroll 60s linear infinite;
            white-space: nowrap;
        `;

        // Add tech terms to marquee
        techTerms.forEach(term => {
            const span = document.createElement('span');
            span.textContent = term;
            span.style.cssText = `
                font-size: 4rem;
                font-weight: 800;
                margin-right: 4rem;
                color: #00d4ff;
                text-transform: uppercase;
            `;
            marqueeContent.appendChild(span);
        });

        marqueeContainer.appendChild(marqueeContent);

        // Add to hero section
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            heroSection.appendChild(marqueeContainer);
        }

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes marqueeScroll {
                0% { transform: translateX(100%); }
                100% { transform: translateX(-100%); }
            }
        `;
        document.head.appendChild(style);
    }

    // Particle effect for background
    function createParticleEffect() {
        const canvas = document.createElement('canvas');
        canvas.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.3;
        `;
        document.body.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        let particles = [];

        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }

        function createParticle() {
            return {
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 2 + 1,
                opacity: Math.random() * 0.5 + 0.2
            };
        }

        function initParticles() {
            particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push(createParticle());
            }
        }

        function updateParticles() {
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;

                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            });
        }

        function drawParticles() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            particles.forEach(particle => {
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(0, 212, 255, ${particle.opacity})`;
                ctx.fill();
            });

            // Draw connections
            particles.forEach((particle, i) => {
                particles.slice(i + 1).forEach(otherParticle => {
                    const distance = Math.sqrt(
                        Math.pow(particle.x - otherParticle.x, 2) +
                        Math.pow(particle.y - otherParticle.y, 2)
                    );

                    if (distance < 100) {
                        ctx.beginPath();
                        ctx.moveTo(particle.x, particle.y);
                        ctx.lineTo(otherParticle.x, otherParticle.y);
                        ctx.strokeStyle = `rgba(0, 212, 255, ${0.1 * (1 - distance / 100)})`;
                        ctx.stroke();
                    }
                });
            });
        }

        function animate() {
            updateParticles();
            drawParticles();
            requestAnimationFrame(animate);
        }

        resizeCanvas();
        initParticles();
        animate();

        window.addEventListener('resize', () => {
            resizeCanvas();
            initParticles();
        });
    }

    // Typing effect for hero subtitle
    function createTypingEffect() {
        const subtitle = document.querySelector('.hero-subtitle');
        if (subtitle) {
            const text = subtitle.textContent;
            subtitle.textContent = '';
            subtitle.style.borderRight = '2px solid #00d4ff';

            let i = 0;
            function typeWriter() {
                if (i < text.length) {
                    subtitle.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 100);
                } else {
                    // Remove cursor after typing is complete
                    setTimeout(() => {
                        subtitle.style.borderRight = 'none';
                    }, 1000);
                }
            }

            // Start typing after a delay
            setTimeout(typeWriter, 1000);
        }
    }

    // Initialize all effects
    createTechMarquee();
    createParticleEffect();
    createTypingEffect();

    console.log('AIvent marquee and effects loaded!');
});
